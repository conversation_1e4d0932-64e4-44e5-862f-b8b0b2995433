["tests/test_data/test_feature_importance.py::test_empty_importance_values", "tests/test_data/test_feature_importance.py::test_importance_calculation_correctness", "tests/test_data/test_feature_importance.py::test_importance_calculation_failure", "tests/test_data/test_feature_importance.py::test_importance_fallback_to_lagged_corr", "tests/test_data/test_feature_importance.py::test_importance_for_dimension_constraint", "tests/test_data/test_feature_importance.py::test_importance_threshold_filtering[0.05]", "tests/test_data/test_feature_importance.py::test_importance_threshold_filtering[0.15]", "tests/test_data/test_feature_importance.py::test_importance_threshold_filtering[0.1]", "tests/test_data/test_feature_importance.py::test_importance_threshold_filtering[0.2]", "tests/test_data/test_feature_importance.py::test_importance_with_missing_values", "tests/test_data/test_importance_performance.py::test_importance_threshold_impact", "tests/test_data/test_importance_performance.py::test_importance_vs_full_feature_performance", "tests/test_data/test_importance_performance.py::test_importance_vs_other_selection_methods", "tests/test_data/test_importance_performance.py::test_importance_vs_random_selection", "tests/test_data/test_interlayer_nan_handling.py::TestInterlayerNaNHandling::test_handle_interlayer_nan_all_nan_column", "tests/test_data/test_interlayer_nan_handling.py::TestInterlayerNaNHandling::test_handle_interlayer_nan_device_consistency", "tests/test_data/test_interlayer_nan_handling.py::TestInterlayerNaNHandling::test_handle_interlayer_nan_empty_tensor", "tests/test_data/test_interlayer_nan_handling.py::TestInterlayerNaNHandling::test_handle_interlayer_nan_forward_fill", "tests/test_data/test_interlayer_nan_handling.py::TestInterlayerNaNHandling::test_handle_interlayer_nan_high_ratio_error", "tests/test_data/test_interlayer_nan_handling.py::TestInterlayerNaNHandling::test_handle_interlayer_nan_no_nan", "tests/test_data/test_interlayer_nan_handling.py::TestInterlayerNaNHandling::test_handle_interlayer_nan_single_row", "tests/test_data/test_interlayer_nan_handling.py::TestInterlayerNaNHandling::test_handle_interlayer_nan_with_nan", "tests/test_data/test_interlayer_nan_integration.py::TestInterlayerNaNIntegration::test_interlayer_nan_handling_in_enhance_features", "tests/test_data/test_interlayer_nan_integration.py::TestInterlayerNaNIntegration::test_multiple_layers_with_nan_propagation", "tests/test_data/test_interlayer_nan_integration.py::TestInterlayerNaNIntegration::test_nan_ratio_threshold_enforcement", "tests/test_data/test_lightgbm_importance.py::test_feature_importance_stability", "tests/test_data/test_lightgbm_importance.py::test_feature_importance_types", "tests/test_data/test_lightgbm_importance.py::test_importance_vs_prediction_power", "tests/test_data/test_lightgbm_importance.py::test_importance_with_different_dataset_sizes", "tests/test_data/test_lightgbm_importance.py::test_importance_with_outliers", "tests/test_models/test_gan/test_adaptive_noise_fix.py::TestAdaptiveNoiseFix::test_conditional_noise_generator_no_warning", "tests/test_models/test_gan/test_adaptive_noise_fix.py::TestAdaptiveNoiseFix::test_correlation_matrix_always_positive_definite", "tests/test_models/test_gan/test_adaptive_noise_fix.py::TestAdaptiveNoiseFix::test_extreme_input_stability", "tests/test_models/test_gan/test_adaptive_noise_fix.py::TestAdaptiveNoiseFix::test_gradient_flow", "tests/test_models/test_gan/test_adaptive_noise_fix.py::TestAdaptiveNoiseFix::test_network_output_dimensions", "tests/test_utils/test_config_parameter_source.py::TestConfigDependencyAndIntegrity::test_config_cross_validation_no_defaults", "tests/test_utils/test_config_parameter_source.py::TestConfigDependencyAndIntegrity::test_config_type_consistency_no_defaults", "tests/test_utils/test_config_parameter_source.py::TestConfigDependencyAndIntegrity::test_nested_config_parameter_source", "tests/test_utils/test_config_parameter_source.py::TestConfigIntegrityAndBoundaryConditions::test_config_boundary_value_validation", "tests/test_utils/test_config_parameter_source.py::TestConfigIntegrityAndBoundaryConditions::test_config_circular_dependency_detection", "tests/test_utils/test_config_parameter_source.py::TestConfigIntegrityAndBoundaryConditions::test_config_missing_required_sections", "tests/test_utils/test_config_parameter_source.py::TestConfigIntegrityAndBoundaryConditions::test_config_parameter_consistency_across_modules", "tests/test_utils/test_config_parameter_source.py::TestConfigSecurityAndSensitiveData::test_config_file_permissions_security", "tests/test_utils/test_config_parameter_source.py::TestConfigSecurityAndSensitiveData::test_config_validation_injection_prevention", "tests/test_utils/test_config_parameter_source.py::TestConfigSecurityAndSensitiveData::test_no_hardcoded_credentials_in_config", "tests/test_utils/test_config_parameter_source.py::TestDataPipelineParameterSource::test_data_loader_parameter_source", "tests/test_utils/test_config_parameter_source.py::TestDataPipelineParameterSource::test_data_preprocessor_parameter_source", "tests/test_utils/test_config_parameter_source.py::TestDataPipelineParameterSource::test_feature_engineering_parameter_source", "tests/test_utils/test_config_parameter_source.py::TestDeepParameterSourceAnalysis::test_base_config_no_defaults", "tests/test_utils/test_config_parameter_source.py::TestDeepParameterSourceAnalysis::test_config_dataclass_no_default_values", "tests/test_utils/test_config_parameter_source.py::TestDeepParameterSourceAnalysis::test_config_loader_no_fallback_logic", "tests/test_utils/test_config_parameter_source.py::TestDeepParameterSourceAnalysis::test_no_environment_variable_fallback", "tests/test_utils/test_config_parameter_source.py::TestDynamicConfigAndRuntimeParameters::test_cuda_manager_parameter_source", "tests/test_utils/test_config_parameter_source.py::TestDynamicConfigAndRuntimeParameters::test_optimizer_manager_parameter_source", "tests/test_utils/test_config_parameter_source.py::TestDynamicConfigAndRuntimeParameters::test_resource_manager_parameter_source", "tests/test_utils/test_config_parameter_source.py::TestModelArchitectureParameterSource::test_attention_mechanism_parameter_source", "tests/test_utils/test_config_parameter_source.py::TestModelArchitectureParameterSource::test_discriminator_parameter_source", "tests/test_utils/test_config_parameter_source.py::TestModelArchitectureParameterSource::test_generator_parameter_source", "tests/test_utils/test_config_parameter_source.py::TestModuleSpecificParameterSource::test_feature_selector_parameter_source", "tests/test_utils/test_config_parameter_source.py::TestModuleSpecificParameterSource::test_model_parameter_source_detection", "tests/test_utils/test_config_parameter_source.py::TestModuleSpecificParameterSource::test_training_parameter_source_detection", "tests/test_utils/test_config_parameter_source.py::TestParameterSourceDetection::test_config_manager_no_default_config_method", "tests/test_utils/test_config_parameter_source.py::TestParameterSourceDetection::test_config_validation_strictness", "tests/test_utils/test_config_parameter_source.py::TestParameterSourceDetection::test_hardcoded_constants_detection", "tests/test_utils/test_config_parameter_source.py::TestParameterSourceDetection::test_no_default_value_fallback", "tests/test_utils/test_config_parameter_source.py::TestParameterSourceDetection::test_no_hardcoded_defaults_in_config_loading", "tests/test_utils/test_config_parameter_source.py::TestParameterSourceDetection::test_parameter_exploration_defaults_detection", "tests/test_utils/test_config_parameter_source.py::TestParameterSourceDetection::test_parameter_traceability_to_config_file"]