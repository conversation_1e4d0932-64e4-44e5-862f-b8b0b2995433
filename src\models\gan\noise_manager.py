"""噪声管理模块 - 负责GAN模型的噪声生成和验证

项目结构模块索引：
1. 父类模块:
   - src/models/base/base_model.py: BaseModel，模型基类
2. 同阶段GAN模块:
   - gan_model.py: GAN主模型
   - generator.py: 时序生成器
   - discriminator.py: 时序判别器
"""

import math
from dataclasses import dataclass, field
from typing import Any

import torch
from torch import nn

from src.utils.logger import LoggerFactory


@dataclass
class NoiseConfig:
    """噪声配置"""
    dim: int
    distribution: str
    scale: float
    seed: int | None
    dtype: torch.dtype
    # 新增结构化噪声配置
    structured: bool
    temporal_correlation: float  # 时间相关性系数
    feature_correlation: float   # 特征相关性系数
    noise_patterns: list[str]

class NoiseManager:
    """噪声生成和管理器"""

    def __init__(self, config: NoiseConfig):
        """初始化噪声生成器

        Args:
            config: 噪声配置
        """
        self.config = config
        self._logger = LoggerFactory().get_logger(self.__class__.__name__)
        self._logger.info(f"初始化噪声生成器 - 维度: {config.dim}, 类型: {config.distribution}")

    def configure(self, **kwargs):
        """配置噪声生成参数

        Args:
            **kwargs: 配置参数
                - distribution: 分布类型 ('normal' 或 'uniform')
                - scale: 缩放因子
                - seed: 随机种子
                - dtype: 数据类型
                - dim: 噪声维度
        """
        for k, v in kwargs.items():
            if hasattr(self.config, k):
                setattr(self.config, k, v)
        self._logger.info(f"更新噪声配置: {kwargs}")

    def generate(
        self,
        batch_size: int,
        seq_length: int,
        return_info: bool = False
    ) -> torch.Tensor | tuple[torch.Tensor, dict[str, Any]]:
        """生成噪声张量

        Args:
            batch_size: 批次大小
            seq_length: 序列长度
            return_info: 是否返回噪声信息

        Returns:
            Union[torch.Tensor, Tuple[torch.Tensor, Dict]]:
                - 噪声张量 [batch_size, seq_length, noise_dim]
                - 可选的噪声信息字典
        """
        # 检查是否使用结构化噪声
        if self.config.structured:
            noise = self._generate_structured_noise(batch_size, seq_length)
        # 生成基础噪声
        elif self.config.distribution == 'normal':
            noise = torch.randn(
                batch_size,
                seq_length,
                self.config.dim,
                dtype=self.config.dtype
            )
        else:
            noise = torch.rand(
                batch_size,
                seq_length,
                self.config.dim,
                dtype=self.config.dtype
            ) * 2 - 1

        # 应用缩放因子
        noise = noise * self.config.scale

        if return_info:
            info = {
                'distribution': self.config.distribution,
                'scale': self.config.scale,
                'dim': self.config.dim,
                'structured': self.config.structured
            }
            return noise, info
        return noise

    def _generate_structured_noise(
        self,
        batch_size: int,
        seq_length: int
    ) -> torch.Tensor:
        """生成结构化噪声

        结构化噪声包含以下特性：
        1. 时间相关性：相邻时间步的噪声具有相关性
        2. 特征相关性：不同特征维度之间具有相关性
        3. 模式多样性：包含趋势、季节性和随机模式

        Args:
            batch_size: 批次大小
            seq_length: 序列长度

        Returns:
            torch.Tensor: 结构化噪声张量 [batch_size, seq_length, noise_dim]
        """
        self._logger.info(f"生成结构化噪声 - 批次大小: {batch_size}, 序列长度: {seq_length}, 维度: {self.config.dim}")

        # 1. 初始化基础噪声
        base_noise = torch.randn(
            batch_size,
            seq_length,
            self.config.dim,
            dtype=self.config.dtype
        )

        # 2. 生成时间相关噪声
        temporal_noise = self._generate_temporal_correlated_noise(batch_size, seq_length)

        # 3. 生成特征相关噪声
        feature_noise = self._generate_feature_correlated_noise(batch_size, seq_length)

        # 4. 生成模式噪声
        pattern_noise = self._generate_pattern_noise(batch_size, seq_length)

        # 5. 组合各类噪声
        # 使用加权平均组合不同类型的噪声
        combined_noise = (
            0.2 * base_noise +
            0.3 * temporal_noise +
            0.2 * feature_noise +
            0.3 * pattern_noise
        )

        # 标准化组合噪声
        combined_noise = (combined_noise - combined_noise.mean()) / (combined_noise.std() + 1e-8)

        return combined_noise

    def _generate_temporal_correlated_noise(
        self,
        batch_size: int,
        seq_length: int
    ) -> torch.Tensor:
        """生成时间相关噪声

        使用自回归过程生成时间相关噪声

        Args:
            batch_size: 批次大小
            seq_length: 序列长度

        Returns:
            torch.Tensor: 时间相关噪声张量 [batch_size, seq_length, noise_dim]
        """
        # 初始化噪声张量
        noise = torch.zeros(
            batch_size,
            seq_length,
            self.config.dim,
            dtype=self.config.dtype
        )

        # 生成第一个时间步的噪声
        noise[:, 0, :] = torch.randn(
            batch_size,
            self.config.dim,
            dtype=self.config.dtype
        )

        # 使用自回归过程生成后续时间步的噪声
        for t in range(1, seq_length):
            # AR(1)过程: x_t = ρ * x_{t-1} + ε_t
            ar_component = self.config.temporal_correlation * noise[:, t-1, :]
            innovation = torch.randn(
                batch_size,
                self.config.dim,
                dtype=self.config.dtype
            ) * math.sqrt(1 - self.config.temporal_correlation**2)

            noise[:, t, :] = ar_component + innovation

        return noise

    def _generate_feature_correlated_noise(
        self,
        batch_size: int,
        seq_length: int
    ) -> torch.Tensor:
        """生成特征相关噪声

        使用多元正态分布生成特征间相关的噪声

        Args:
            batch_size: 批次大小
            seq_length: 序列长度

        Returns:
            torch.Tensor: 特征相关噪声张量 [batch_size, seq_length, noise_dim]
        """
        # 创建相关性矩阵
        # 默认为单位矩阵
        correlation_matrix = torch.eye(self.config.dim, dtype=self.config.dtype)

        # 添加特征间相关性
        # 对于非对角元素，添加相关性
        for i in range(self.config.dim):
            for j in range(i+1, self.config.dim):
                # 生成一个基于特征相关性参数的相关系数
                # 使用距离函数使得距离较近的特征相关性更高
                corr_value = self.config.feature_correlation * (1 - abs(i - j) / self.config.dim)
                correlation_matrix[i, j] = corr_value
                correlation_matrix[j, i] = corr_value  # 对称矩阵

        # 确保相关性矩阵是正定的
        # 添加小的对角项以确保正定性
        correlation_matrix = correlation_matrix + 0.01 * torch.eye(self.config.dim, dtype=self.config.dtype)

        # 生成多元正态噪声
        noise = torch.randn(
            batch_size,
            seq_length,
            self.config.dim,
            dtype=self.config.dtype
        )

        # 将相关性矩阵应用到噪声上
        # 使用Cholesky分解实现多元正态分布
        try:
            # 尝试使用Cholesky分解
            L = torch.linalg.cholesky(correlation_matrix)

            # 将相关性应用到噪声
            # 对每个批次和时间步应用相同的相关性
            for b in range(batch_size):
                for t in range(seq_length):
                    noise[b, t, :] = torch.matmul(L, noise[b, t, :])
        except Exception as e:
            error_msg = f"使用Cholesky分解生成相关噪声失败: {e}"
            self._logger.error(error_msg)
            # 移除回退机制，直接抛出异常
            raise RuntimeError(error_msg) from e

        return noise

    def _generate_pattern_noise(
        self,
        batch_size: int,
        seq_length: int
    ) -> torch.Tensor:
        """生成包含特定模式的噪声

        生成包含趋势、季节性和随机模式的噪声

        Args:
            batch_size: 批次大小
            seq_length: 序列长度

        Returns:
            torch.Tensor: 模式噪声张量 [batch_size, seq_length, noise_dim]
        """
        # 初始化噪声张量
        pattern_noise = torch.zeros(
            batch_size,
            seq_length,
            self.config.dim,
            dtype=self.config.dtype
        )

        # 生成时间步序列
        time_steps = torch.arange(seq_length, dtype=self.config.dtype).unsqueeze(0).unsqueeze(-1)
        time_steps = time_steps.repeat(batch_size, 1, self.config.dim)

        # 生成不同的模式
        patterns = {}

        # 1. 趋势模式 - 线性趋势
        if 'trend' in self.config.noise_patterns:
            # 为每个批次和特征生成不同的趋势斜率
            slopes = torch.randn(batch_size, 1, self.config.dim, dtype=self.config.dtype) * 0.1
            trends = slopes * time_steps / seq_length
            patterns['trend'] = trends

        # 2. 季节性模式 - 正弦波
        if 'seasonal' in self.config.noise_patterns:
            # 为每个批次和特征生成不同的频率和相位
            frequencies = torch.rand(batch_size, 1, self.config.dim, dtype=self.config.dtype) * 4 + 1  # 1-5个周期
            phases = torch.rand(batch_size, 1, self.config.dim, dtype=self.config.dtype) * 2 * math.pi
            amplitudes = torch.rand(batch_size, 1, self.config.dim, dtype=self.config.dtype) * 0.5 + 0.5  # 0.5-1.0的振幅

            # 计算季节性模式
            seasonals = amplitudes * torch.sin(frequencies * 2 * math.pi * time_steps / seq_length + phases)
            patterns['seasonal'] = seasonals

        # 3. 随机模式 - 白噪声
        if 'random' in self.config.noise_patterns:
            randoms = torch.randn(batch_size, seq_length, self.config.dim, dtype=self.config.dtype) * 0.2
            patterns['random'] = randoms

        # 4. 突变模式 - 在随机位置添加突变
        if 'spike' in self.config.noise_patterns:
            spikes = torch.zeros(batch_size, seq_length, self.config.dim, dtype=self.config.dtype)

            # 生成突变模式
            # 使用更简单的方法：生成少量的大幅度随机元素
            # 生成少量的大幅度随机元素
            spikes = torch.zeros(batch_size, seq_length, self.config.dim, dtype=self.config.dtype)
            # 生成少量的大幅度随机元素，大约占怰10%的元素
            mask = torch.rand(batch_size, seq_length, self.config.dim) < 0.1
            # 在随机位置生成大幅度的突变
            # 使用正确的size参数格式
            num_spikes = int(mask.sum().item())  # 确保是整数
            spikes[mask] = torch.randn(size=(num_spikes,), dtype=self.config.dtype) * 2.0

            patterns['spike'] = spikes

        # 组合所有模式
        if patterns:
            # 平均分配权重
            weight_per_pattern = 1.0 / len(patterns)

            # 使用循环变量名称避免未使用警告
            for _, pattern in patterns.items():
                pattern_noise += pattern * weight_per_pattern

        return pattern_noise

    def validate(self, input_noise: torch.Tensor) -> bool:
        """验证噪声张量

        Args:
            input_noise: 输入噪声张量

        Returns:
            bool: 验证是否通过
        """
        if not isinstance(input_noise, torch.Tensor):
            self._logger.warning(f"噪声类型错误: {type(input_noise)}")
            return False

        if input_noise.dim() != 3:
            self._logger.warning(f"噪声维度错误: {input_noise.dim()}")
            return False

        if input_noise.size(-1) != self.config.dim:
            self._logger.warning(
                f"噪声维度不匹配: 期望{self.config.dim}, 实际{input_noise.size(-1)}"
            )
            return False

        if torch.isnan(input_noise).any() or torch.isinf(input_noise).any():
            self._logger.warning("噪声包含NaN/Inf值")
            return False

        return True

    def generate_conditional(
        self,
        batch_size: int,
        condition: torch.Tensor,
        seq_length: int | None = None,
        device: torch.device | None = None
    ) -> torch.Tensor:
        """生成条件噪声

        Args:
            batch_size: 批次大小
            condition: 条件张量
            seq_length: 序列长度，如果为None则使用condition的序列长度
            device: 设备，如果为None则使用condition的设备

        Returns:
            torch.Tensor: 生成的条件噪声
        """
        # 获取条件形状
        if seq_length is None:
            seq_length = condition.size(1) if condition.dim() > 1 else 1

        # 获取设备
        if device is None and isinstance(condition, torch.Tensor):
            device = condition.device

        # 生成基础噪声 - 确保只获取噪声张量，不需要额外信息
        noise = self.generate(batch_size, seq_length, return_info=False)
        # 使用类型断言来明确告诉类型检查器noise是torch.Tensor
        assert isinstance(noise, torch.Tensor), "生成的噪声应该是张量类型"

        # 将噪声移动到与条件相同的设备
        if device is not None:
            noise = noise.to(device)

        # 将条件信息融合到噪声中
        if isinstance(condition, torch.Tensor) and condition.dim() == noise.dim():
            # 确保条件和噪声维度匹配
            if condition.size(-1) != noise.size(-1):
                # 如果维度不匹配，使用线性投影
                projection = nn.Linear(
                    condition.size(-1), noise.size(-1)
                ).to(device)
                condition = projection(condition)

            # 融合条件和噪声
            noise = noise + 0.1 * condition

        return noise
