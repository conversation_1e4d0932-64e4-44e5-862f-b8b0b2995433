"""模型基类模块 - 提供深度学习模型的基础功能框架

项目结构模块索引：
1. 基础设施模块:
   - src/utils/config_manager.py: 配置管理，模型参数控制
   - src/utils/logger.py: 日志系统，训练过程记录
   - src/utils/cuda_manager.py: 系统监控，资源使用追踪
   - src/utils/path_utils.py: 路径工具，模型存储管理
   - src/utils/resource_manager.py: 资源管理，内存与设备优化
   - src/utils/exception_handler.py: 异常处理，训练错误恢复
   - src/utils/cuda_manager.py: GPU管理，计算资源调度

2. 共用模块:
   - src/data/data_loader.py: 数据加载，训练数据处理
   - src/data/metrics_calculator.py: 指标计算，模型评估
   - src/data/window_dataset.py: 窗口数据集，时序处理

3. 配置文件：
   - config.yaml:
     ├── model:
     │   ├── base:
     │   │   ├── input_dim: 输入维度
     │   │   ├── output_dim: 输出维度
     │   │   └── hidden_dim: 隐藏维度
     │   ├── training:
     │   │   ├── batch_size: 批次大小
     │   │   ├── max_epochs: 最大训练轮数
     │   │   └── early_stop: 早停配置
     │   └── optimization:
     │       ├── optimizer: 优化器配置
     │       ├── scheduler: 学习率调度配置
     │       └── loss: 损失函数配置
     └── system:
         ├── device: 设备配置
         └── precision: 计算精度配置

4. 父类模块：
   - src/models/base/base_module.py: BaseModule，基础功能模块
   - src/models/base/model_state.py: ModelState，状态管理器
   - src/models/base/model_saver.py: ModelSaver，存储管理器
   - src/models/base/signal_processor.py: SignalProcessor，信号处理

5. 同阶段基础模型模块：
   - model_state.py: 模型状态管理
   - base_module.py: 基础模块功能
   - signal_processor.py: 信号处理功能
   - model_saver.py: 模型存储功能

核心功能：
1. 模型生命周期管理
   - 初始化与配置
   - 训练循环控制
   - 验证过程管理
   - 资源释放与清理

2. 训练流程抽象
   - 数据批处理
   - 梯度计算与更新
   - 损失函数管理
   - 指标计算与记录

3. 优化器管理
   - 参数更新控制
   - 学习率调度
   - 梯度裁剪处理
   - 混合精度训练

4. 错误处理与恢复
   - 异常捕获与记录
   - 状态恢复机制
   - 资源清理保障
   - 故障转移策略

5. 资源监控与优化
   - 内存使用追踪
   - GPU资源监控
   - 批处理大小优化
   - 缓存管理机制
"""

import traceback
from typing import Any
import json
from pathlib import Path

import torch
from torch import nn, optim

from src.models.base.model_state import ModelStateManager

# 导入混合精度管理模块
from src.utils.amp_manager import get_amp_manager
from src.utils.config_manager import ConfigManager
from src.utils.cuda import cuda_manager
from src.utils.logger import get_logger

from .base_module import BaseModule


class PathEncoder(json.JSONEncoder):
    """自定义JSON编码器，用于处理特殊类型"""
    def default(self, obj):
        """处理特殊类型的序列化

        Args:
            obj: 需要序列化的对象

        Returns:
            可序列化的对象
        """
        # 处理Path对象
        if isinstance(obj, Path):
            return str(obj)

        # 处理MixedPrecisionConfig对象
        if hasattr(obj, '__dict__'):
            return obj.__dict__

        # 处理其他不可序列化的对象
        return str(obj)


class BaseModel(BaseModule):
    """模型基类

    提供以下基础功能：
    1. 配置管理
    2. 设备管理
    3. 参数统计
    4. 优化器管理
    5. 学习率调度
    """

    def __init__(self, config_manager: ConfigManager, name: str = "BaseModel"):
        """初始化基础模型

        Args:
            config_manager: 配置管理器实例
            name: 模型名称
        """
        super().__init__(name)
        self._config_manager = config_manager
        self._name = name
        # 新增设备信息记录
        self.device = cuda_manager.device
        self._logger = get_logger(self.__class__.__name__)

        try:
            # 记录配置对象的类型和结构
            self._logger.info(f"配置对象类型检查:\n"
                              f"- config_manager类型: {type(self._config_manager)}\n"
                              f"- training类型: {type(self._config_manager.training)}\n"
                              f"- mixed_precision类型: {type(self._config_manager.training.mixed_precision)}\n"
                              f"- mixed_precision属性: {dir(self._config_manager.training.mixed_precision)}")

            # 记录配置信息
            self._logger.info(f"模型初始化开始，配置信息:\n"
                              f"- 模型名称: {name}\n"
                              f"- 设备: {self.device}\n"
                              f"- 训练配置: {json.dumps(self._config_manager.training.__dict__, ensure_ascii=False, indent=2, cls=PathEncoder)}\n"
                              f"- 混合精度配置: {json.dumps(self._config_manager.training.mixed_precision.__dict__, ensure_ascii=False, indent=2, cls=PathEncoder)}")

            # 验证配置
            self._validate_config()

            # 设置混合精度配置 (从 training 配置读取)
            if config_manager.training.mixed_precision is None:
                error_msg = "配置验证失败: mixed_precision配置不能为None"
                self._logger.error(error_msg)
                raise ValueError(error_msg)

            self.use_amp = config_manager.training.mixed_precision.enabled
            if self.use_amp:
                # 初始化混合精度
                self.init_mixed_precision(config_manager.training.mixed_precision)
                self._logger.info(
                    f"启用混合精度训练:\n"
                    f"- 初始scale: {config_manager.training.mixed_precision.init_scale}\n"
                    f"- 增长因子: {config_manager.training.mixed_precision.growth_factor}\n"
                    f"- 回退因子: {config_manager.training.mixed_precision.backoff_factor}\n"
                    f"- 增长间隔: {config_manager.training.mixed_precision.growth_interval}"
                )

            # 记录初始化信息
            self._logger.info(
                f"\n{self.__class__.__name__}初始化完成:\n"
                f"- 配置版本: {config_manager.version}\n"
                f"- 设备: {cuda_manager.device}\n"
                f"- 混合精度: {'启用' if self.use_amp else '禁用'}"
            )

        except Exception as e:
            self._logger.error(f"模型初始化失败: {e!s}")
            raise RuntimeError(f"模型初始化失败: {e!s}") from e

    @property
    def config_manager(self) -> ConfigManager:
        """获取配置管理器"""
        return self._config_manager

    def _validate_config(self):
        """验证配置有效性"""
        try:
            self._logger.info("开始验证配置...")

            # 检查必要的属性
            if not hasattr(self._config_manager, 'model'):
                error_msg = "配置验证失败: 缺少model配置"
                self._logger.error(error_msg)
                raise ValueError(error_msg)

            # 检查model.type属性
            if not hasattr(self._config_manager.model, 'type'):
                error_msg = "配置验证失败: model配置中缺少type字段"
                self._logger.error(error_msg)
                # 确保日志被刷新
                for handler in self._logger.handlers:
                    handler.flush()
                raise ValueError(error_msg)

            # 检查混合精度配置，如果不存在则抛出错误
            if not hasattr(self._config_manager, 'training'):
                error_msg = "配置验证失败: 缺少training配置"
                self._logger.error(error_msg)
                raise ValueError(error_msg)

            if not hasattr(self._config_manager.training, 'mixed_precision'):
                error_msg = "配置验证失败: 缺少mixed_precision配置"
                self._logger.error(error_msg)
                raise ValueError(error_msg)

            # 获取混合精度配置
            mixed_precision = self._config_manager.training.mixed_precision
            try:
                # 尝试序列化配置对象
                config_json = json.dumps(mixed_precision.__dict__, ensure_ascii=False, indent=2, cls=PathEncoder)
                self._logger.info(f"混合精度配置详细信息:\n"
                                  f"- 类型: {type(mixed_precision)}\n"
                                  f"- 属性列表: {dir(mixed_precision)}\n"
                                  f"- 字典内容: {mixed_precision.__dict__}\n"
                                  f"- 配置内容: {config_json}")
            except Exception as e:
                error_msg = f"序列化混合精度配置失败: {e!s}\n"
                self._logger.error(error_msg)
                raise ValueError(error_msg) from e

            required_fields = ['enabled', 'dtype', 'init_scale', 'growth_factor', 'backoff_factor', 'growth_interval']

            # 检查必要字段是否存在
            missing_fields = []
            for field in required_fields:
                if not hasattr(mixed_precision, field):
                    missing_fields.append(field)
                    self._logger.info(f"缺少字段 {field}，当前属性: {dir(mixed_precision)}")

            if missing_fields:
                error_msg = f"配置验证失败: mixed_precision配置中缺少字段: {', '.join(missing_fields)}"
                self._logger.error(error_msg)
                raise ValueError(error_msg)

            self._logger.info("配置验证通过")

        except Exception as e:
            error_msg = f"配置验证失败: {e!s}"
            self._logger.error(error_msg)
            raise ValueError(error_msg) from e

    def train(self, mode: bool = True) -> 'BaseModel':
        """切换训练模式"""
        super().train(mode)
        return self

    def eval(self) -> 'BaseModel':
        """切换评估模式"""
        return self.train(False)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播

        Args:
            x: 输入张量

        Returns:
            torch.Tensor: 输出张量

        Raises:
            ValueError: 如果输入张量无效
        """
        # 验证输入张量
        self.validate_tensor(x, "模型输入", expected_dims=2)

        # 子类必须实现具体的前向传播逻辑
        raise NotImplementedError("子类必须实现forward方法")

    def get_loss_fn(self) -> nn.Module:
        """获取损失函数

        Returns:
            nn.Module: 损失函数模块
        """
        try:
            # 检查配置中是否存在loss_type
            if not hasattr(self._config_manager.model, 'loss_type'):
                error_msg = "配置缺少必要参数: model.loss_type"
                self._logger.error(error_msg)
                raise ValueError(error_msg)

            loss_type = self._config_manager.model.loss_type

            # 如果是Mock对象且没有设置loss_type，抛出异常
            if hasattr(loss_type, '__class__') and 'Mock' in str(loss_type.__class__):
                error_msg = "配置中的loss_type是Mock对象，无法确定实际类型"
                self._logger.error(error_msg)
                raise ValueError(error_msg)

            loss_fns = {
                'mse': nn.MSELoss(),
                'mae': nn.L1Loss(),
                'huber': nn.SmoothL1Loss()
            }

            if loss_type not in loss_fns:
                raise ValueError(f"不支持的损失函数类型: {loss_type}")

            loss_fn = loss_fns[loss_type]
            self._logger.info(f"使用损失函数: {loss_type}")

            return loss_fn

        except Exception as e:
            self._logger.error(f"获取损失函数失败: {e!s}")
            raise RuntimeError(f"获取损失函数失败: {e!s}") from e

    def get_optimizer(self, parameters=None, **kwargs) -> optim.Optimizer:
        """获取优化器 (已弃用 - 请使用 OptimizerManager.create_optimizer)

        Args:
            parameters: 要优化的参数（可选）
            **kwargs: 额外的优化器参数

        Returns:
            optim.Optimizer: 优化器实例

        Raises:
            NotImplementedError: 此方法已被弃用，应使用 OptimizerManager。
        """
        # --- 修改：标记为弃用并抛出错误 ---
        error_msg = (
            "BaseModel.get_optimizer 已弃用。请使用 OptimizerManager.create_optimizer "
            "并提供 'model_type' ('generator' 或 'discriminator') 来获取具有正确学习率的优化器。"
        )
        self._logger.error(error_msg)
        raise NotImplementedError(error_msg)
        # --- 结束修改 ---

    def _get_scheduler(
        self,
        optimizer: torch.optim.Optimizer
    ) -> torch.optim.lr_scheduler.CyclicLR | None:
        """获取学习率调度器

        Args:
            optimizer: 优化器实例

        Returns:
            Optional[torch.optim.lr_scheduler.CyclicLR]: 调度器实例

        Note:
            此方法已过时，请使用 scheduler_g 和 scheduler_d 配置。
        """
        try:
            # 检查是否存在调度器配置
            if not hasattr(self._config_manager.training, 'scheduler_g') or not hasattr(self._config_manager.training, 'scheduler_d'):
                error_msg = "配置缺失必要参数: training.scheduler_g 或 training.scheduler_d"
                self._logger.error(error_msg)
                raise ValueError(error_msg)

            # 此方法已过时，应使用 scheduler_g 和 scheduler_d 配置
            self._logger.warning("调用过时的 _get_scheduler 方法，请使用 scheduler_g 和 scheduler_d 配置")

            # 不使用调度器，返回 None
            self._logger.info("不使用学习率调度器")
            return None

        except Exception as e:
            self._logger.error(f"创建学习率调度器失败: {e!s}")
            raise RuntimeError(f"创建学习率调度器失败: {e!s}") from e

    def _validate_scheduler(self, scheduler: torch.optim.lr_scheduler.CyclicLR | None) -> None:
        """验证学习率调度器

        Args:
            scheduler: 调度器实例
        """
        if scheduler is None:
            return

        try:
            # 验证调度器类型
            if not isinstance(scheduler, optim.lr_scheduler.CyclicLR):
                raise TypeError(f"不支持的调度器类型: {type(scheduler)}")

            # 验证调度器参数
            if not hasattr(scheduler, 'base_lrs') or not scheduler.base_lrs:
                raise ValueError("调度器缺少base_lrs参数")
            if not hasattr(scheduler, 'max_lrs') or not scheduler.max_lrs:
                raise ValueError("调度器缺少max_lrs参数")

            self._logger.debug("学习率调度器验证通过")

        except Exception as e:
            self._logger.error(f"学习率调度器验证失败: {e!s}")
            raise ValueError(f"学习率调度器验证失败: {e!s}") from e

    def _init_scheduler(self) -> None:
        """初始化学习率调度器
        """
        try:
            if not hasattr(self, 'optimizer'):
                raise RuntimeError("必须先初始化优化器")

            self.scheduler = self._get_scheduler(self.optimizer)

            if self.scheduler is not None:
                self._logger.info("学习率调度器初始化完成")

        except Exception as e:
            self._logger.error(f"学习率调度器初始化失败: {e!s}")
            raise RuntimeError(f"学习率调度器初始化失败: {e!s}") from e

    # 将 batch_size 设为必需参数，移除默认值 4
    # 注意：此 fit 方法在 GANTrainer 中未被使用，GANTrainer 使用 run_training_loop
    def fit(self, train_dataset, batch_size: int, val_dataset=None, num_workers: int = 0) -> dict[str, Any]:
        """训练模型 (注意：此方法在 GANTrainer 中未使用)

        Args:
            train_dataset: 训练数据集
            batch_size: 批次大小 (必需)
            val_dataset: 验证数据集（可选）
            num_workers: 数据加载线程数

        Returns:
            Dict[str, Any]: 训练历史
        """
        try:
            # 创建数据加载器
            train_loader = torch.utils.data.DataLoader(
                train_dataset,
                batch_size=batch_size,
                shuffle=True,
                num_workers=num_workers
            )

            if val_dataset is not None:
                val_loader = torch.utils.data.DataLoader(
                    val_dataset,
                    batch_size=batch_size,
                    shuffle=False,
                    num_workers=num_workers
                )
            else:
                val_loader = None

            # 初始化优化器和调度器
            self.optimizer = self.get_optimizer()
            self._init_scheduler()

            # 记录训练配置
            self._logger.info(
                f"\n开始训练:\n"
                f"- 训练集大小: {len(train_dataset)}\n"
                f"- 验证集大小: {len(val_dataset) if val_dataset else 'N/A'}\n"
                f"- 批次大小: {batch_size}\n"
                f"- 工作线程: {num_workers}\n"
                f"- 设备: {cuda_manager.device}"
            )

            # 训练循环
            history = {
                'train_loss': [],
                'val_loss': [] if val_loader else None
            }

            num_epochs = self._config_manager.training.num_epochs
            # 添加断言确保 num_epochs 是整数
            assert isinstance(num_epochs, int), f"num_epochs 必须是整数，但得到的是 {type(num_epochs)}"
            for epoch in range(num_epochs):
                # 每个epoch开始时监控资源
                self._monitor_resources()

                # 每100个batch监控一次资源
                batch_count = len(train_loader)
                for i in range(batch_count):
                    if i % 100 == 0:
                        self._monitor_resources()
                # 训练一轮
                train_metrics = self._train_epoch(train_loader)
                history['train_loss'].append(train_metrics['loss'])

                # 验证
                if val_loader is not None:
                    val_metrics = self._validate_epoch(val_loader)
                    history['val_loss'].append(val_metrics['loss'])

                    # 更新学习率 - 注意：此处的调用在 GANTrainer 流程中不会发生
                    # self.update_learning_rate(val_metrics) # 已移除重复逻辑

                    self._logger.info(
                        f"\n轮次 {epoch+1}/{num_epochs}:\n"
                        f"- 训练损失: {train_metrics['loss']:.4f}\n"
                        f"- 验证损失: {val_metrics['loss']:.4f}"
                    )
                else:
                    self._logger.info(
                        f"\n轮次 {epoch+1}/{num_epochs}:\n"
                        f"- 训练损失: {train_metrics['loss']:.4f}"
                    )

            self._logger.info("训练完成")
            return history

        except Exception as e:
            self._logger.error(f"训练失败: {e!s}")
            raise RuntimeError(f"训练失败: {e!s}") from e

    def _train_epoch(self, train_loader) -> dict[str, float]:
        """训练一个轮次

        Args:
            train_loader: 训练数据加载器

        Returns:
            Dict[str, float]: 训练指标
        """
        self.train()
        total_loss = 0
        num_batches = len(train_loader)

        try:
            for batch_idx, batch in enumerate(train_loader):
                # 获取数据并确保在正确设备上
                x = cuda_manager.move_to_device(batch['features'])
                y = cuda_manager.move_to_device(batch['target'])

                # 清零梯度
                self.optimizer.zero_grad()

                # 前向传播 (确保模型参数也在正确设备上)
                self.to(cuda_manager.device)
                output = self(x)

                # 计算损失 - 直接比较输出和目标
                # output shape: [batch_size, output_dim] -> [4, 1]
                # y shape: [batch_size, target_dim] -> [4, 1]
                loss = self.get_loss_fn()(output, y)

                # 反向传播
                if self.use_amp:
                    # 混合精度训练流程
                    with self.amp_manager.autocast_context():
                        # 重新计算损失，确保在autocast上下文中
                        output = self(x)
                        loss = self.get_loss_fn()(output, y)

                    # 缩放损失并反向传播
                    scaled_loss = self.amp_manager.scale_loss(loss)
                    scaled_loss.backward()

                    # 执行优化器步骤
                    self.amp_manager.step_optimizer(self.optimizer)
                    self.amp_manager.update_scaler()
                else:
                    # 普通训练流程
                    loss.backward()
                    self.optimizer.step()

                # 累积损失
                total_loss += loss.item()

                # 记录进度
                if (batch_idx + 1) % 10 == 0:
                    self._logger.info( # Changed from debug to info
                        f"训练进度: {batch_idx+1}/{num_batches} "
                        f"[{(batch_idx+1)/num_batches*100:.1f}%]"
                    )

            # 计算平均损失
            avg_loss = total_loss / num_batches

            return {'loss': avg_loss}

        except Exception as e:
            self._logger.error(f"训练轮次失败: {e!s}")
            raise RuntimeError(f"训练轮次失败: {e!s}") from e

    def _validate_epoch(self, val_loader) -> dict[str, float]:
        """验证一个轮次

        Args:
            val_loader: 验证数据加载器

        Returns:
            Dict[str, float]: 验证指标
        """
        self.eval()
        total_loss = 0
        num_batches = len(val_loader)

        try:
            with torch.no_grad():
                for batch_idx, batch in enumerate(val_loader):
                    # 获取数据
                    x = batch['features'].to(cuda_manager.device)
                    y = batch['target'].to(cuda_manager.device)

                    # 前向传播，使用混合精度上下文
                    if self.use_amp:
                        with self.amp_manager.autocast_context():
                            output = self(x)
                            loss = self.get_loss_fn()(output, y)
                    else:
                        output = self(x)
                        loss = self.get_loss_fn()(output, y)

                    # 累积损失
                    total_loss += loss.item()

                    # 记录进度
                    if (batch_idx + 1) % 10 == 0:
                        self._logger.info( # Changed from debug to info
                            f"验证进度: {batch_idx+1}/{num_batches} "
                            f"[{(batch_idx+1)/num_batches*100:.1f}%]"
                        )

            # 计算平均损失
            avg_loss = total_loss / num_batches

            return {'loss': avg_loss}

        except Exception as e:
            self._logger.error(f"验证轮次失败: {e!s}")
            raise RuntimeError(f"验证轮次失败: {e!s}") from e

    def set_input_dim(self, input_dim: int):
        """设置输入维度

        Args:
            input_dim: 输入维度
        """
        self._input_dim = input_dim
        self._logger.info(f"设置输入维度: {input_dim}")

    def get_optimizer_config(self) -> dict[str, Any]:
        """获取优化器配置

        Returns:
            Dict[str, Any]: 优化器配置字典，包含以下字段：
                - type: 优化器类型 (str)
                - weight_decay: 权重衰减 (float, 默认0.0)
                - beta1: Adam优化器参数 (float, 默认0.9)
                - beta2: Adam优化器参数 (float, 默认0.999)
                - eps: Adam优化器参数 (float, 默认1e-8)
                - momentum: SGD优化器参数 (float, 默认0.9)

        Raises:
            ValueError: 如果缺少必需配置或配置无效
            RuntimeError: 如果获取配置失败
        """
        try:
            if not hasattr(self._config_manager, 'training'):
                error_msg = "配置中缺少 training 配置项"
                self._logger.error(error_msg)
                raise ValueError(error_msg)

            if not hasattr(self._config_manager.training, 'optimizer'):
                error_msg = "配置中缺少 training.optimizer 配置项"
                self._logger.error(error_msg)
                raise ValueError(error_msg)

            optim_config = self._config_manager.training.optimizer

            # 检查必需字段
            # 检查必需字段 type 是否存在且不为 None
            optim_type = getattr(optim_config, 'type', None)
            if optim_type is None:
                error_msg = "配置中缺少 training.optimizer.type 配置项或其值为 None"
                self._logger.error(error_msg)
                raise ValueError(error_msg)

            # 基本配置
            config: dict[str, Any] = {
                'type': str(optim_type).lower(),
            }

            # 检查并获取weight_decay
            if not hasattr(optim_config, 'weight_decay'):
                raise ValueError("配置缺少weight_decay参数")
            weight_decay_val = optim_config.weight_decay
            if weight_decay_val is None:
                raise ValueError("weight_decay参数不能为None")
            try:
                weight_decay = float(weight_decay_val)
                if weight_decay < 0:
                    raise ValueError("weight_decay必须大于等于0")
                config['weight_decay'] = weight_decay
            except (TypeError, ValueError) as e:
                error_msg = f"无效的weight_decay配置: {e!s}"
                self._logger.error(error_msg)
                raise ValueError(error_msg) from e

            # 根据优化器类型处理特定参数
            if config['type'] == 'adam':
                required_params = ['beta1', 'beta2', 'eps']
                for param in required_params:
                    if not hasattr(optim_config, param):
                        raise ValueError(f"Adam优化器配置缺少{param}参数")
                    param_val = getattr(optim_config, param)
                    if param_val is None:
                        raise ValueError(f"{param}参数不能为None")
                    try:
                        value = float(param_val)
                        if param in ['beta1', 'beta2']:
                            if not (0 <= value < 1):
                                raise ValueError(f"{param}必须在[0,1)范围内")
                        elif param == 'eps' and value <= 0:
                            raise ValueError("eps必须大于0")
                        config[param] = value
                    except (TypeError, ValueError) as e:
                        error_msg = f"无效的{param}配置: {e!s}"
                        self._logger.error(error_msg)
                        raise ValueError(error_msg) from e

            elif config['type'] == 'sgd':
                if not hasattr(optim_config, 'momentum'):
                    raise ValueError("SGD优化器配置缺少momentum参数")
                momentum_val = optim_config.momentum
                if momentum_val is None:
                    raise ValueError("momentum参数不能为None")
                try:
                    momentum = float(momentum_val)
                    if not (0 <= momentum < 1):
                        raise ValueError("momentum必须在[0,1)范围内")
                    config['momentum'] = momentum
                except (TypeError, ValueError) as e:
                    error_msg = f"无效的momentum配置: {e!s}"
                    self._logger.error(error_msg)
                    raise ValueError(error_msg) from e

            return config

        except ValueError as e:
            # 直接重新抛出ValueError，避免重复包装错误消息
            raise e
        except Exception as e:
            self._logger.error(f"获取优化器配置失败: {e!s}")
            raise RuntimeError(f"获取优化器配置失败: {e!s}") from e


    def handle_error_with_options(self, error: Exception, context: str) -> None:
        """错误处理方法，移除可选抛出异常的设计

        Args:
            error: 异常对象
            context: 错误上下文描述

        Raises:
            RuntimeError: 总是抛出异常
        """
        error_msg = f"{context}:\n- 错误类型: {type(error).__name__}\n- 错误信息: {error!s}"
        self.logger.error(error_msg)
        self.logger.debug(f"错误堆栈:\n{traceback.format_exc()}")

        # 记录当前状态，model_state_manager必须存在
        if not hasattr(self, 'model_state_manager') or self.model_state_manager is None:
            error_msg_state = "model_state_manager 不存在，无法记录错误状态"
            self.logger.error(error_msg_state)
            raise ValueError(error_msg_state)

        if not isinstance(self.model_state_manager, ModelStateManager):
            error_msg_type = f"模型状态管理器类型错误: {type(self.model_state_manager)}"
            self.logger.error(error_msg_type)
            raise ValueError(error_msg_type)

        self.model_state_manager.set_error(error_msg)

        if torch.cuda.is_available():
            device_info = (
                f"\n设备状态:\n"
                f"- GPU内存使用: {torch.cuda.memory_allocated() / 1024**2:.1f}MB\n"
                f"- GPU内存缓存: {torch.cuda.memory_reserved() / 1024**2:.1f}MB"
            )
            self.logger.debug(device_info)

        # 移除可选抛出异常的设计，总是抛出异常
        raise RuntimeError(error_msg) from error

    def init_mixed_precision(self, config):
        """初始化混合精度训练

        Args:
            config: 混合精度配置对象

        Raises:
            RuntimeError: 如果初始化失败
        """
        try:
            # 记录配置对象信息
            self._logger.info("初始化混合精度训练:")
            self._logger.info(f"- 配置对象类型: {type(config)}")
            if hasattr(config, '__dict__'):
                self._logger.info(f"- 配置对象属性: {dir(config)}")
                self._logger.info(f"- 配置对象内容: {config.__dict__}")

            # 验证配置
            if not hasattr(config, 'enabled'):
                error_msg = "混合精度配置缺少enabled字段"
                self._logger.error(error_msg)
                raise ValueError(error_msg)

            # 使用混合精度管理模块
            try:
                self.amp_manager = get_amp_manager(self._name, config)
            except Exception as e:
                error_msg = f"创建混合精度管理器失败: {e!s}"
                self._logger.error(error_msg)
                raise RuntimeError(error_msg) from e

            # 设置混合精度状态
            self.use_amp = self.amp_manager.enabled

            # 为了兼容性，设置 scaler 属性
            self.scaler = self.amp_manager.scaler

            # 记录初始化信息
            status_text = "启用" if self.use_amp else "禁用"
            self._logger.info("LogPoint1_MP_Init_Done")
            self._logger.info("LogPoint2_MP_Status: " + str(status_text))
            self._logger.info("LogPoint3_MP_Manager: " + str(self.amp_manager.name))
            self._logger.info("LogPoint4_MP_Dtype: " + str(self.amp_manager.dtype))
            self._logger.info("LogPoint5_MP_InitScale: " + str(self.amp_manager.init_scale))
            self._logger.info("LogPoint6_MP_GrowthFactor: " + str(self.amp_manager.growth_factor))
            self._logger.info("LogPoint7_MP_BackoffFactor: " + str(self.amp_manager.backoff_factor))
            self._logger.info("LogPoint8_MP_GrowthInterval: " + str(self.amp_manager.growth_interval))

        except Exception as e:
            raise e

    def _initialize_weights(self, module: nn.Module) -> None:
        """初始化模型权重"""
        if isinstance(module, nn.Linear):
            nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                nn.init.constant_(module.bias, 0.0)

    def _monitor_resources(self):
        """监控资源使用情况"""
        # 移除异常处理的回退机制，如果监控失败应该抛出异常
        if cuda_manager and cuda_manager.is_cuda_available:
            memory_stats = cuda_manager.get_memory_stats()

            self._logger.debug(
                f"GPU内存使用:\n"
                f"- 已分配: {memory_stats['allocated_mb']:.1f}MB\n"
                f"- 已缓存: {memory_stats['reserved_mb']:.1f}MB\n"
                f"- 峰值: {memory_stats['max_allocated_mb']:.1f}MB"
            )

    def _get_gpu_memory_info(self) -> str:
        """获取GPU内存使用信息"""
        # 移除异常处理的回退机制，如果获取失败应该抛出异常
        if not cuda_manager or not cuda_manager.is_cuda_available:
            return "N/A"

        memory_stats = cuda_manager.get_memory_stats()
        return f"已分配={memory_stats['allocated_mb']:.1f}MB, 缓存={memory_stats['reserved_mb']:.1f}MB"

    def log_module_info(self):
        """记录模块信息"""
        # 移除异常处理的回退机制，如果记录失败应该抛出异常
        info = (
            f"\n模块信息 - {self.name}:\n"
            f"- 参数数量: {self.count_parameters():,}\n"
            f"- 设备: {next(self.parameters()).device if any(True for _ in self.parameters()) else 'N/A'}\n"
            f"- 主要子模块:"
        )
        for name, module in self.named_children():
            info += f"\n  - {name}: {module.__class__.__name__}"
        self.logger.info(info)
