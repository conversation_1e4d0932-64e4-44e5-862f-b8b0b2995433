"""
自适应梯度惩罚权重管理器

提供动态调整GAN训练中梯度惩罚权重(lambda_gp)的功能，
以提高训练稳定性和性能。
"""
import logging
import math
from logging import Logger
from typing import Any

import torch

from src.utils.adaptive_lambda_gp_config import AdaptiveLambdaGPConfig


class AdaptiveLambdaGP:
    """自适应梯度惩罚权重管理器"""

    def __init__(
        self,
        config: AdaptiveLambdaGPConfig | None = None,
        use_self_attention: bool = False,
        window_size: int = 48,
        logger: Logger | None = None
    ):
        """
        初始化自适应梯度惩罚权重管理器

        Args:
            config: 配置对象，如果为None则使用默认配置
            use_self_attention: 是否使用自注意力机制
            window_size: 窗口大小
            logger: 日志记录器，如果为None则创建新的记录器
        """
        # 设置配置
        self.config = config or AdaptiveLambdaGPConfig()

        # 确保启用自适应梯度惩罚权重
        if not hasattr(self.config, 'enabled') or not self.config.enabled:
            self.config.enabled = True

        # 设置日志记录器
        self.logger = logger or logging.getLogger("AdaptiveLambdaGP")

        # 保存模型配置
        self.use_self_attention = use_self_attention
        self.window_size = window_size

        # 设置基准lambda_gp值
        self.base_lambda_gp = self._determine_base_lambda_gp()

        # 状态变量 - 初始值使用基准值
        self.current_lambda_gp = self.base_lambda_gp
        self.step_count = 0
        self.grad_history: list[float] = []
        self.lambda_history: list[float] = []
        self.ema_grad_norm: float | None = None

        # 记录初始化信息
        self.logger.info(f"初始化自适应梯度惩罚权重管理器: base_lambda_gp={self.base_lambda_gp}, "
                         f"use_self_attention={use_self_attention}, window_size={window_size}")

    def _determine_base_lambda_gp(self) -> float:
        """根据模型配置确定基准lambda_gp值"""
        # 如果配置中提供了基准值，直接使用
        if self.config.base_lambda_gp is not None:
            return self.config.base_lambda_gp

        # 否则，抛出异常要求明确配置
        raise ValueError("配置中必须提供 base_lambda_gp 值")

    def get_lambda_gp(self) -> float:
        """获取当前的lambda_gp值"""
        return self.current_lambda_gp

    def update(self, discriminator_model: torch.nn.Module) -> float:
        """
        根据判别器模型的梯度更新lambda_gp

        Args:
            discriminator_model: 判别器模型

        Returns:
            float: 更新后的lambda_gp值
        """
        # 如果未启用，直接返回基准值
        if not self.config.enabled:
            self.logger.info(f"自适应梯度惩罚权重未启用，返回基准值: {self.base_lambda_gp}")
            return self.base_lambda_gp

        self.logger.debug(f"自适应梯度惩罚权重已启用，当前值: {self.current_lambda_gp}")

        self.step_count += 1

        # 在预热期使用固定的lambda_gp
        if self.step_count <= self.config.warmup_steps:
            if self.config.verbose_logging and self.step_count % 10 == 0:
                self.logger.info(f"步骤 {self.step_count}: 预热期，使用固定lambda_gp={self.current_lambda_gp:.4f}")
            return self.current_lambda_gp

        # 每隔update_interval步更新一次
        if self.step_count % self.config.update_interval != 0:
            return self.current_lambda_gp

        # 计算判别器梯度范数
        grad_norm = self._compute_gradient_norm(discriminator_model)

        # 检查梯度范数是否有效
        if not math.isfinite(grad_norm) or grad_norm <= 0:
            self.logger.warning(f"步骤 {self.step_count}: 检测到无效梯度范数: {grad_norm}，保持lambda_gp不变")
            # 添加一个默认值到历史记录中，以保持历史记录的连续性
            self.grad_history.append(0.0 if not math.isfinite(grad_norm) else grad_norm)
            self.lambda_history.append(self.current_lambda_gp)
            return self.current_lambda_gp

        # 添加到历史记录
        self.grad_history.append(grad_norm)

        # 更新指数移动平均梯度范数
        if self.ema_grad_norm is None:
            self.ema_grad_norm = grad_norm
        else:
            self.ema_grad_norm = self.config.smoothing_factor * self.ema_grad_norm + (1 - self.config.smoothing_factor) * grad_norm

        # 根据梯度范数调整lambda_gp
        new_lambda_gp = self._adjust_lambda_gp(self.ema_grad_norm)

        # 记录调整信息
        if self.config.verbose_logging:
            self.logger.info(f"步骤 {self.step_count}: 梯度范数={grad_norm:.4f}, EMA梯度范数={self.ema_grad_norm:.4f}, "
                            f"lambda_gp: {self.current_lambda_gp:.4f} -> {new_lambda_gp:.4f}")

        # 更新当前值
        self.current_lambda_gp = new_lambda_gp
        self.lambda_history.append(new_lambda_gp)

        return self.current_lambda_gp

    def _compute_gradient_norm(self, model: torch.nn.Module) -> float:
        """计算模型梯度的L2范数"""
        total_norm = 0.0
        param_count = 0

        self.logger.debug("开始计算模型梯度范数...")

        # 确保模型有梯度
        has_grad = False
        for param in model.parameters():
            if param.grad is not None:
                has_grad = True
                break

        if not has_grad:
            self.logger.warning("模型没有梯度，可能是因为还没有执行反向传播")
            return 0.0

        for param in model.parameters():
            if param.grad is not None:
                param_norm = param.grad.data.norm(2)
                total_norm += param_norm.item() ** 2
                param_count += 1

        if param_count > 0:
            total_norm = total_norm ** 0.5
            self.logger.debug(f"计算得到梯度范数: {total_norm:.4f}，参数数量: {param_count}")
            return total_norm
        else:
            self.logger.warning("没有参数有梯度，返回梯度范数: 0.0")
            return 0.0

    def _adjust_lambda_gp(self, grad_norm: float) -> float:
        """根据梯度范数调整lambda_gp"""
        # 计算梯度范数与目标的偏差
        norm_ratio = grad_norm / self.config.grad_norm_target

        # 如果梯度范数在容忍范围内，不调整lambda_gp
        if abs(norm_ratio - 1.0) <= self.config.grad_norm_tolerance:
            return self.current_lambda_gp

        # 计算调整量
        if norm_ratio > 1.0:
            # 梯度范数过大，增加lambda_gp
            adjustment = self.config.adaptation_rate * (norm_ratio - 1.0) * self.current_lambda_gp
        else:
            # 梯度范数过小，减少lambda_gp
            adjustment = -self.config.adaptation_rate * (1.0 - norm_ratio) * self.current_lambda_gp

        # 应用调整，并确保在允许范围内
        new_lambda_gp = self.current_lambda_gp + adjustment

        # 使用用户配置的min_lambda_gp值，不再强制调整
        min_lambda_gp = self.config.min_lambda_gp

        # 限制在允许范围内
        new_lambda_gp = max(min_lambda_gp, min(self.config.max_lambda_gp, new_lambda_gp))

        return new_lambda_gp

    def get_stats(self) -> dict[str, Any]:
        """获取统计信息"""
        return {
            'base_lambda_gp': self.base_lambda_gp,
            'current_lambda_gp': self.current_lambda_gp,
            'step_count': self.step_count,
            'grad_history': self.grad_history[-10:] if len(self.grad_history) > 0 else [],
            'lambda_history': self.lambda_history[-10:] if len(self.lambda_history) > 0 else [],
            'ema_grad_norm': self.ema_grad_norm,
            'use_self_attention': self.use_self_attention,
            'window_size': self.window_size
        }

    # 已删除 plot_history 方法

    def reset(self) -> None:
        """重置状态"""
        self.current_lambda_gp = self.base_lambda_gp
        self.step_count = 0
        self.grad_history = []
        self.lambda_history = []
        self.ema_grad_norm = None
        self.logger.info(f"已重置自适应梯度惩罚权重管理器，当前lambda_gp={self.current_lambda_gp:.4f}")

    @classmethod
    def from_config_dict(cls,
                         config_dict: dict[str, Any],
                         use_self_attention: bool = False,
                         window_size: int = 48,
                         logger: Logger | None = None) -> 'AdaptiveLambdaGP':
        """
        从配置字典创建实例

        Args:
            config_dict: 配置字典
            use_self_attention: 是否使用自注意力机制
            window_size: 窗口大小
            logger: 日志记录器

        Returns:
            AdaptiveLambdaGP: 创建的实例
        """
        config = AdaptiveLambdaGPConfig.from_dict(config_dict)
        return cls(config=config, use_self_attention=use_self_attention, window_size=window_size, logger=logger)


# 创建全局实例
adaptive_lambda_gp_manager = None


def configure_adaptive_lambda_gp(
    config_dict: dict[str, Any],
    use_self_attention: bool = False,
    window_size: int = 48,
    logger: Logger | None = None
) -> AdaptiveLambdaGP:
    """
    配置全局自适应梯度惩罚权重管理器

    Args:
        config_dict: 配置字典
        use_self_attention: 是否使用自注意力机制
        window_size: 窗口大小
        logger: 日志记录器

    Returns:
        AdaptiveLambdaGP: 配置的全局实例
    """
    global adaptive_lambda_gp_manager
    adaptive_lambda_gp_manager = AdaptiveLambdaGP.from_config_dict(
        config_dict=config_dict,
        use_self_attention=use_self_attention,
        window_size=window_size,
        logger=logger
    )
    return adaptive_lambda_gp_manager


def get_adaptive_lambda_gp() -> AdaptiveLambdaGP | None:
    """
    获取全局自适应梯度惩罚权重管理器

    Returns:
        Optional[AdaptiveLambdaGP]: 全局实例，如果未配置则返回None
    """
    return adaptive_lambda_gp_manager
