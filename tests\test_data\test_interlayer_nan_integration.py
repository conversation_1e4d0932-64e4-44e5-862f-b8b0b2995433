"""测试层级间空值处理机制的集成测试

验证层级间空值处理机制在实际特征工程流程中的工作情况。
"""

import unittest
import numpy as np
import torch
import pandas as pd
from unittest.mock import Mock, patch

from src.data.feature_engineering.feature_manager import FeatureManager
from src.utils.config.manager import ConfigManager
from src.utils.logger import get_logger


class TestInterlayerNaNIntegration(unittest.TestCase):
    """测试层级间空值处理机制的集成测试"""

    def setUp(self):
        """设置测试环境"""
        self.logger = get_logger(__name__)
        
        # 创建模拟配置
        self.mock_config = Mock(spec=ConfigManager)
        self.mock_config.feature_engineering = Mock()
        self.mock_config.feature_engineering.enable = True
        self.mock_config.feature_engineering.columns = Mock()
        self.mock_config.feature_engineering.columns.time_features = ['date']
        self.mock_config.feature_engineering.columns.categorical = []
        
        # 添加时间预处理配置
        self.mock_config.feature_engineering.time_preprocessing = Mock()
        self.mock_config.feature_engineering.time_preprocessing.features_to_extract = [
            'year', 'month', 'day', 'dayofweek'
        ]
        
        # 添加层级配置
        self.mock_config.feature_engineering.layers = [
            Mock(level=0, generators=['base_features'], keep_input_features=True),
            Mock(level=1, generators=['diff'], keep_input_features=True),
        ]
        self.mock_config.feature_engineering.keep_original_in_final = True
        
        self.mock_config.data = Mock()
        self.mock_config.data.feature_dim = None
        self.mock_config.data.target = 'target'
        
        # 创建FeatureManager实例
        with patch.object(FeatureManager, '_initialize_generators'):
            with patch.object(FeatureManager, '_configure_encoder'):
                with patch('src.data.feature_engineering.feature_manager.TimeFeatureGenerator') as mock_time_gen:
                    mock_time_gen.return_value.is_enabled = False
                    self.feature_manager = FeatureManager(self.mock_config)

    def test_interlayer_nan_handling_in_enhance_features(self):
        """测试在enhance_features流程中的层级间空值处理"""
        # 创建模拟的特征生成器，会产生NaN值
        mock_diff_generator = Mock()
        mock_diff_generator.is_enabled = True
        mock_diff_generator.generate.return_value = torch.tensor([
            [1.0, float('nan'), 3.0, 4.0, 5.0],  # 第一层输出包含NaN
            [2.0, 3.0, float('nan'), 6.0, 7.0],
            [3.0, 4.0, 5.0, float('nan'), 9.0]
        ], dtype=torch.float32)
        mock_diff_generator.get_feature_names.return_value = ['diff1_f0', 'diff1_f1', 'diff1_f2', 'diff1_f3', 'diff1_f4']
        
        # 设置生成器
        self.feature_manager.generators = {'diff': mock_diff_generator}
        
        # 创建输入数据
        input_data = torch.tensor([
            [1.0, 2.0, 3.0],
            [4.0, 5.0, 6.0],
            [7.0, 8.0, 9.0]
        ], dtype=torch.float32)
        
        # 调用enhance_features
        result_tensor, result_names = self.feature_manager.enhance_features(input_data)
        
        # 验证结果：应该没有NaN值
        self.assertFalse(torch.isnan(result_tensor).any(), "结果张量不应包含NaN值")
        
        # 验证形状
        expected_features = 3 + 5  # 原始3个特征 + 5个衍生特征
        self.assertEqual(result_tensor.shape[1], expected_features)
        self.assertEqual(len(result_names), 5)  # 只返回衍生特征名称

    def test_multiple_layers_with_nan_propagation(self):
        """测试多层级中NaN值的传播和处理"""
        # 创建两个模拟生成器
        mock_diff_generator = Mock()
        mock_diff_generator.is_enabled = True
        mock_diff_generator.generate.return_value = torch.tensor([
            [1.0, float('nan'), 3.0],  # 第一层产生NaN
            [2.0, 3.0, float('nan')],
            [3.0, 4.0, 5.0]
        ], dtype=torch.float32)
        mock_diff_generator.get_feature_names.return_value = ['diff1_f0', 'diff1_f1', 'diff1_f2']
        
        mock_window_generator = Mock()
        mock_window_generator.is_enabled = True
        # 第二层生成器接收处理后的数据（应该没有NaN）
        def window_generate_side_effect(data, **kwargs):
            # 验证输入数据没有NaN
            self.assertFalse(torch.isnan(data).any(), "第二层生成器不应接收到NaN值")
            return torch.tensor([
                [10.0, 11.0],
                [12.0, 13.0],
                [14.0, 15.0]
            ], dtype=torch.float32)
        
        mock_window_generator.generate.side_effect = window_generate_side_effect
        mock_window_generator.get_feature_names.return_value = ['window_f0', 'window_f1']
        
        # 设置生成器
        self.feature_manager.generators = {
            'diff': mock_diff_generator,
            'window': mock_window_generator
        }
        
        # 更新层级配置
        self.mock_config.feature_engineering.layers = [
            Mock(level=0, generators=['base_features'], keep_input_features=True),
            Mock(level=1, generators=['diff'], keep_input_features=True),
            Mock(level=2, generators=['window'], keep_input_features=True),
        ]
        
        # 创建输入数据
        input_data = torch.tensor([
            [1.0, 2.0],
            [4.0, 5.0],
            [7.0, 8.0]
        ], dtype=torch.float32)
        
        # 调用enhance_features
        result_tensor, result_names = self.feature_manager.enhance_features(input_data)
        
        # 验证结果：应该没有NaN值
        self.assertFalse(torch.isnan(result_tensor).any(), "最终结果不应包含NaN值")
        
        # 验证第二层生成器确实被调用了
        mock_window_generator.generate.assert_called_once()

    def test_nan_ratio_threshold_enforcement(self):
        """测试NaN比例阈值的强制执行"""
        # 创建会产生过多NaN的生成器
        mock_generator = Mock()
        mock_generator.is_enabled = True
        mock_generator.generate.return_value = torch.tensor([
            [float('nan'), float('nan'), 3.0],  # 超过30%的NaN
            [float('nan'), float('nan'), 6.0],
            [float('nan'), float('nan'), 9.0]
        ], dtype=torch.float32)
        mock_generator.get_feature_names.return_value = ['bad_f0', 'bad_f1', 'bad_f2']
        
        self.feature_manager.generators = {'bad_gen': mock_generator}
        
        # 更新层级配置
        self.mock_config.feature_engineering.layers = [
            Mock(level=0, generators=['base_features'], keep_input_features=True),
            Mock(level=1, generators=['bad_gen'], keep_input_features=True),
        ]
        
        # 创建输入数据
        input_data = torch.tensor([
            [1.0, 2.0],
            [4.0, 5.0],
            [7.0, 8.0]
        ], dtype=torch.float32)
        
        # 应该抛出ValueError
        with self.assertRaises(ValueError) as context:
            self.feature_manager.enhance_features(input_data)
        
        self.assertIn("NaN值比例过高", str(context.exception))


if __name__ == '__main__':
    unittest.main()
