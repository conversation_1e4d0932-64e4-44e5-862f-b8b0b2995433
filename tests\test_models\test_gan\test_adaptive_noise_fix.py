"""测试自适应噪声模块的Cholesky分解修复"""

import pytest
import torch
import torch.nn as nn

from src.models.gan.components.adaptive_noise import ConditionalNoiseGenerator


class TestAdaptiveNoiseFix:
    """测试自适应噪声Cholesky分解修复"""

    def test_conditional_noise_generator_no_warning(self):
        """测试条件噪声生成器不再产生Cholesky分解失败警告"""
        # 设置参数
        feature_dim = 32
        noise_dim = 16
        batch_size = 4
        seq_len = 10
        
        # 创建条件噪声生成器
        generator = ConditionalNoiseGenerator(feature_dim, noise_dim)
        
        # 创建测试输入
        features = torch.randn(batch_size, seq_len, feature_dim)
        base_noise = torch.randn(batch_size, seq_len, noise_dim)
        
        # 测试前向传播 - 应该不会产生警告或异常
        with torch.no_grad():
            conditional_noise = generator(features, base_noise)
        
        # 验证输出形状
        assert conditional_noise.shape == (batch_size, seq_len, noise_dim)
        
        # 验证输出不包含NaN或Inf
        assert not torch.isnan(conditional_noise).any()
        assert not torch.isinf(conditional_noise).any()
        
        # 验证输出是有限的
        assert torch.isfinite(conditional_noise).all()

    def test_correlation_matrix_always_positive_definite(self):
        """测试相关性矩阵始终是正定的"""
        feature_dim = 16
        noise_dim = 8
        batch_size = 2
        seq_len = 5
        
        generator = ConditionalNoiseGenerator(feature_dim, noise_dim)
        features = torch.randn(batch_size, seq_len, feature_dim)
        
        # 多次测试以确保稳定性
        for _ in range(10):
            with torch.no_grad():
                # 获取特征统计
                feature_stats = generator.stats_analyzer(features)
                correlation_params = generator.correlation_net(feature_stats)
                
                # 构造下三角矩阵L
                tril_indices = torch.tril_indices(noise_dim, noise_dim, device=features.device)
                L = torch.zeros(batch_size, seq_len, noise_dim, noise_dim, device=features.device)
                L[..., tril_indices[0], tril_indices[1]] = correlation_params
                
                # 确保对角线元素为正
                diag_indices = torch.arange(noise_dim, device=features.device)
                L[..., diag_indices, diag_indices] = torch.nn.functional.softplus(
                    L[..., diag_indices, diag_indices]
                ) + 1e-6
                
                # 验证L的对角线元素都是正数
                diag_elements = L[..., diag_indices, diag_indices]
                assert (diag_elements > 0).all(), "对角线元素必须为正"
                
                # 验证L * L^T 是正定的（通过检查特征值）
                correlation_matrix = torch.matmul(L, L.transpose(-2, -1))
                
                # 检查每个批次和时间步的矩阵
                for b in range(batch_size):
                    for t in range(seq_len):
                        matrix = correlation_matrix[b, t]
                        eigenvals = torch.linalg.eigvals(matrix).real
                        assert (eigenvals > -1e-6).all(), f"矩阵不是正半定的，特征值: {eigenvals}"

    def test_network_output_dimensions(self):
        """测试网络输出维度正确"""
        feature_dim = 20
        noise_dim = 12
        
        generator = ConditionalNoiseGenerator(feature_dim, noise_dim)
        
        # 验证correlation_net输出维度
        expected_tril_elements = noise_dim * (noise_dim + 1) // 2
        
        # 创建测试输入
        test_input = torch.randn(1, 1, generator.condition_dim)
        
        with torch.no_grad():
            correlation_output = generator.correlation_net(test_input)
            
        assert correlation_output.size(-1) == expected_tril_elements, \
            f"correlation_net输出维度错误: 期望{expected_tril_elements}, 实际{correlation_output.size(-1)}"

    def test_extreme_input_stability(self):
        """测试极端输入下的数值稳定性"""
        feature_dim = 16
        noise_dim = 8
        batch_size = 2
        seq_len = 3
        
        generator = ConditionalNoiseGenerator(feature_dim, noise_dim)
        
        # 测试极端输入值
        extreme_inputs = [
            torch.ones(batch_size, seq_len, feature_dim) * 100,  # 大正值
            torch.ones(batch_size, seq_len, feature_dim) * -100, # 大负值
            torch.zeros(batch_size, seq_len, feature_dim),       # 零值
            torch.randn(batch_size, seq_len, feature_dim) * 0.001, # 小值
        ]
        
        for features in extreme_inputs:
            with torch.no_grad():
                try:
                    conditional_noise = generator(features)
                    
                    # 验证输出有效性
                    assert not torch.isnan(conditional_noise).any()
                    assert not torch.isinf(conditional_noise).any()
                    assert torch.isfinite(conditional_noise).all()
                    
                except Exception as e:
                    pytest.fail(f"极端输入导致异常: {e}")

    def test_gradient_flow(self):
        """测试梯度流动正常"""
        feature_dim = 16
        noise_dim = 8
        batch_size = 2
        seq_len = 3
        
        generator = ConditionalNoiseGenerator(feature_dim, noise_dim)
        
        # 创建需要梯度的输入
        features = torch.randn(batch_size, seq_len, feature_dim, requires_grad=True)
        
        # 前向传播
        conditional_noise = generator(features)
        
        # 计算损失并反向传播
        loss = conditional_noise.sum()
        loss.backward()
        
        # 验证梯度存在且有限
        assert features.grad is not None
        assert torch.isfinite(features.grad).all()
        assert not torch.isnan(features.grad).any()
        
        # 验证网络参数有梯度
        for param in generator.parameters():
            if param.requires_grad:
                assert param.grad is not None
                assert torch.isfinite(param.grad).all()
