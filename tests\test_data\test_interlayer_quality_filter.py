"""
测试层级间特征质量过滤功能

测试覆盖：
1. 零变化特征检测和删除
2. 极低变化特征检测和删除
3. 特征名称与张量维度一致性
4. 过滤后特征数量阈值检查
5. 配置参数验证
6. 边界情况处理
"""

import unittest
from unittest.mock import Mock, patch
import torch
import numpy as np

from src.data.feature_engineering.feature_manager import FeatureManager
from src.utils.config.data import InterlayerQualityFilterConfig
from src.utils.logger import LoggerFactory


class TestInterlayerQualityFilter(unittest.TestCase):
    """测试层级间特征质量过滤功能"""

    def setUp(self):
        """设置测试环境"""
        self.logger_factory = LoggerFactory()

        # 创建一个简化的特征管理器实例，只用于测试过滤方法
        self.feature_manager = FeatureManager.__new__(FeatureManager)
        self.feature_manager.logger = self.logger_factory.get_logger("FeatureManager")

        # 创建模拟配置
        self.mock_config = Mock()
        self.feature_manager.config = self.mock_config

    def test_filter_disabled(self):
        """测试过滤功能禁用时的行为"""
        # 配置过滤功能为禁用
        filter_config = InterlayerQualityFilterConfig(
            enable=False,
            variance_threshold=0.01,
            min_features_threshold=5
        )
        self.mock_config.feature_engineering.interlayer_quality_filter = filter_config

        # 创建测试数据
        data = torch.tensor([[1.0, 2.0, 3.0],
                            [1.0, 2.1, 3.1],
                            [1.0, 2.2, 3.2]], dtype=torch.float32)
        feature_names = ['const_feature', 'normal_feature', 'another_feature']

        # 执行过滤
        filtered_data, filtered_names = self.feature_manager._filter_low_quality_features(
            data, feature_names, level=1
        )

        # 验证结果：应该返回原始数据
        self.assertTrue(torch.equal(filtered_data, data))
        self.assertEqual(filtered_names, feature_names)

    def test_filter_zero_variance_features(self):
        """测试零变化特征的检测和删除"""
        # 配置过滤功能
        filter_config = InterlayerQualityFilterConfig(
            enable=True,
            variance_threshold=0.01,
            min_features_threshold=1
        )
        self.mock_config.feature_engineering.interlayer_quality_filter = filter_config

        # 创建包含零变化特征的测试数据
        data = torch.tensor([[1.0, 2.0, 5.0],
                            [1.0, 3.0, 5.0],  # 第一列和第三列是常数，第二列变化足够大
                            [1.0, 4.0, 5.0]], dtype=torch.float32)
        feature_names = ['const_feature_1', 'normal_feature', 'const_feature_2']

        # 执行过滤
        filtered_data, filtered_names = self.feature_manager._filter_low_quality_features(
            data, feature_names, level=1
        )

        # 验证结果：应该只保留第二列
        expected_data = data[:, [1]]  # 只保留第二列
        self.assertTrue(torch.equal(filtered_data, expected_data))
        self.assertEqual(filtered_names, ['normal_feature'])

    def test_filter_low_variance_features(self):
        """测试极低变化特征的检测和删除"""
        # 配置过滤功能
        filter_config = InterlayerQualityFilterConfig(
            enable=True,
            variance_threshold=0.01,
            min_features_threshold=1
        )
        self.mock_config.feature_engineering.interlayer_quality_filter = filter_config

        # 创建包含极低变化特征的测试数据
        data = torch.tensor([[1.0, 2.0, 10.0],
                            [1.001, 2.5, 11.0],  # 第一列变化极小
                            [1.002, 3.0, 12.0]], dtype=torch.float32)
        feature_names = ['low_var_feature', 'normal_feature_1', 'normal_feature_2']

        # 执行过滤
        filtered_data, filtered_names = self.feature_manager._filter_low_quality_features(
            data, feature_names, level=1
        )

        # 验证结果：应该删除第一列（方差过小）
        expected_data = data[:, [1, 2]]  # 保留第二、三列
        self.assertTrue(torch.equal(filtered_data, expected_data))
        self.assertEqual(filtered_names, ['normal_feature_1', 'normal_feature_2'])

    def test_min_features_threshold_enforcement(self):
        """测试最小特征数量阈值的强制执行"""
        # 配置过滤功能，设置较高的最小特征阈值
        filter_config = InterlayerQualityFilterConfig(
            enable=True,
            variance_threshold=0.01,
            min_features_threshold=5
        )
        self.mock_config.feature_engineering.interlayer_quality_filter = filter_config

        # 创建只有少量特征的测试数据
        data = torch.tensor([[1.0, 2.0],
                            [1.0, 2.1],
                            [1.0, 2.2]], dtype=torch.float32)
        feature_names = ['const_feature', 'normal_feature']

        # 执行过滤，应该抛出异常
        with self.assertRaises(ValueError) as context:
            self.feature_manager._filter_low_quality_features(
                data, feature_names, level=1
            )

        # 验证异常信息
        self.assertIn("低于最小阈值", str(context.exception))
        self.assertIn("数据质量严重不足", str(context.exception))

    def test_feature_names_consistency_check(self):
        """测试特征名称与张量维度一致性检查"""
        # 配置过滤功能
        filter_config = InterlayerQualityFilterConfig(
            enable=True,
            variance_threshold=0.01,
            min_features_threshold=1
        )
        self.mock_config.feature_engineering.interlayer_quality_filter = filter_config

        # 创建维度不匹配的测试数据
        data = torch.tensor([[1.0, 2.0, 3.0],
                            [1.1, 2.1, 3.1]], dtype=torch.float32)
        feature_names = ['feature_1', 'feature_2']  # 只有2个名称，但数据有3列

        # 执行过滤，应该抛出异常
        with self.assertRaises(ValueError) as context:
            self.feature_manager._filter_low_quality_features(
                data, feature_names, level=1
            )

        # 验证异常信息
        self.assertIn("数据张量特征数", str(context.exception))
        self.assertIn("特征名称数", str(context.exception))
        self.assertIn("不匹配", str(context.exception))

    def test_no_low_quality_features(self):
        """测试没有低质量特征时的行为"""
        # 配置过滤功能
        filter_config = InterlayerQualityFilterConfig(
            enable=True,
            variance_threshold=0.01,
            min_features_threshold=1
        )
        self.mock_config.feature_engineering.interlayer_quality_filter = filter_config

        # 创建所有特征都是高质量的测试数据
        data = torch.tensor([[1.0, 2.0, 10.0],
                            [2.0, 3.0, 15.0],
                            [3.0, 4.0, 20.0]], dtype=torch.float32)
        feature_names = ['feature_1', 'feature_2', 'feature_3']

        # 执行过滤
        filtered_data, filtered_names = self.feature_manager._filter_low_quality_features(
            data, feature_names, level=1
        )

        # 验证结果：应该保留所有特征
        self.assertTrue(torch.equal(filtered_data, data))
        self.assertEqual(filtered_names, feature_names)

    def test_config_validation(self):
        """测试配置验证"""
        # 测试负方差阈值
        with self.assertRaises(ValueError):
            InterlayerQualityFilterConfig(
                enable=True,
                variance_threshold=-0.01,  # 负值
                min_features_threshold=5
            )

        # 测试无效的最小特征阈值
        with self.assertRaises(ValueError):
            InterlayerQualityFilterConfig(
                enable=True,
                variance_threshold=0.01,
                min_features_threshold=0  # 小于1
            )

    def test_missing_config(self):
        """测试缺少配置时的错误处理"""
        # 移除配置
        delattr(self.mock_config, 'feature_engineering')

        data = torch.tensor([[1.0, 2.0]], dtype=torch.float32)
        feature_names = ['feature_1', 'feature_2']

        # 执行过滤，应该抛出异常
        with self.assertRaises(ValueError) as context:
            self.feature_manager._filter_low_quality_features(
                data, feature_names, level=1
            )

        self.assertIn("缺少 feature_engineering 配置项", str(context.exception))


if __name__ == '__main__':
    unittest.main()
